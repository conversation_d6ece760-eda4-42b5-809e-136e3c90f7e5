num=10
num1=[10,20,30,40] 
print(num1)
#datatypes
#numberic- int  ex 10,20,30
#str ex "abhiruchi"
#bool ex:true,false
#tuple ex(20,50,50.89)
#dictionary
list=[50,79,49,89]#list
print(list)
print(list[2])
tpl=(20,68,78)
print(tpl[1])
print(tpl[0:])#with slicing
for ls in list:#loop will start with colon
    print(ls)
list2=["abhiruchi","yeole"]
print(list2[:])
print(list[1:4])
print(list[:3])#print with one less element
print(list[3:])
#create a list with technologies 6 number of element slice that
list3=["c++","python","java","c","react.js","javascript","html","css","c#"]
print(list3[:])
print(list3[0:6])
print(list3[1:8])
print(list3[:8])
print(list3[0:4])
d_dct={'name':'xyz','age':20,'subject':'programming language','per':36.89}
#key:value
print(d_dct['subject'])
print(d_dct['age'])
d_emp={'name':'abhiruchi','designation':'nagpur','salary':55786,'experience':'Software developer 3yrs','city':'amravati'}
print(d_emp['name'])
print(d_emp['salary'])
print(d_emp['city'])
print(d_emp.items())#for accessing whole dict
for i in d_emp:
    print(d_emp)
for k in d_emp.items():
    print(k)
#add pair in existing dict
d_emp['grade']='A+'
print(d_emp)
d_emp['education']='b.tech'
print(d_emp)
   
